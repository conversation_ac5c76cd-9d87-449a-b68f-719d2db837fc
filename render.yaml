services:
  - type: web
    name: carrozzeria-motta-roberto
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
    headers:
      - path: /*
        name: X-Frame-Options
        value: DENY
      - path: /*
        name: X-Content-Type-Options
        value: nosniff
      - path: /static/*
        name: Cache-Control
        value: public, max-age=86400

databases:
  - name: carrozzeria-db
    databaseName: carrozzeria_motta
    user: carrozzeria_user
    plan: free
