/* 🎨 RESET E BASE */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* 🔐 LOGIN SCREEN */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 450px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header h1 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 28px;
    font-weight: 700;
}

.login-header p {
    margin: 0 0 30px 0;
    color: #666;
    font-size: 16px;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 0 15px;
    transition: all 0.3s ease;
}

.input-group:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group i {
    color: #999;
    margin-right: 12px;
    font-size: 18px;
}

.input-group input {
    border: none;
    outline: none;
    padding: 15px 0;
    font-size: 16px;
    flex: 1;
    background: transparent;
    color: #333;
}

.input-group input::placeholder {
    color: #999;
}

.login-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Quick Login Buttons */
.quick-login {
    margin-bottom: 25px;
    padding: 20px 0;
    border-top: 1px solid #e1e5e9;
}

.quick-login h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.quick-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.quick-btn {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    font-weight: 600;
}

.quick-btn:hover {
    border-color: #667eea;
    background: #f8f9ff;
    transform: translateY(-2px);
}

.quick-btn.manager {
    border-color: #28a745;
    color: #28a745;
}

.quick-btn.manager:hover {
    border-color: #28a745;
    background: #f8fff9;
}

.quick-btn.employee {
    border-color: #007bff;
    color: #007bff;
}

.quick-btn.employee:hover {
    border-color: #007bff;
    background: #f8fbff;
}

.quick-btn i {
    font-size: 20px;
}

/* Credentials Info */
.credentials-info {
    border-top: 1px solid #e1e5e9;
    padding-top: 20px;
}

.credentials-info details {
    text-align: left;
}

.credentials-info summary {
    cursor: pointer;
    color: #666;
    font-size: 14px;
    font-weight: 600;
    padding: 10px;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.credentials-info summary:hover {
    background: #e9ecef;
    color: #333;
}

.credentials-list {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.cred-section {
    margin-bottom: 15px;
}

.cred-section:last-child {
    margin-bottom: 0;
}

.cred-section h5 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.cred-section p {
    margin: 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.cred-section code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #495057;
}

/* 🎯 DASHBOARD */
.dashboard {
    display: none;
    min-height: 100vh;
    background: #f5f7fa;
}

.dashboard.active {
    display: block;
}

/* Header */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.company-logo {
    height: 45px;
    width: auto;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    background: white;
    padding: 4px;
}

.header-left h1 {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Navigation Tabs */
.nav-tabs {
    background: white;
    padding: 0 30px;
    display: flex;
    gap: 5px;
    border-bottom: 1px solid #e1e5e9;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.nav-tab {
    background: transparent;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
}

.nav-tab:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border-left: 5px solid;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.urgent {
    border-left-color: #dc3545;
}

.stat-card.active {
    border-left-color: #ffc107;
}

.stat-card.completed {
    border-left-color: #28a745;
}

.stat-card.operators {
    border-left-color: #007bff;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card.urgent .stat-icon {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.stat-card.active .stat-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stat-card.completed .stat-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stat-card.operators .stat-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dashboard-controls, .work-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.control-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.control-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* Toast Container */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    border-left: 5px solid;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left-color: #28a745;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.info {
    border-left-color: #007bff;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .login-container {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .quick-buttons {
        grid-template-columns: 1fr;
    }
    
    .header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .nav-tabs {
        padding: 0 20px;
        overflow-x: auto;
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .quick-stats {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}

/* 🏭 DEPARTMENTS DASHBOARD */
.departments-dashboard {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.departments-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.department-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.department-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.department-card.verniciatura {
    border-color: #e74c3c;
}

.department-card.lattoneria {
    border-color: #f39c12;
}

.department-card.meccanica {
    border-color: #3498db;
}

.department-card.preparazione {
    border-color: #9b59b6;
}

.department-card.lavaggio {
    border-color: #1abc9c;
}

.department-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.department-header h3 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.department-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.department-status.active {
    background: #d4edda;
    color: #155724;
}

.department-operators {
    margin-bottom: 20px;
}

.operator-card {
    background: white;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 10px;
}

.operator-info {
    flex: 1;
}

.operator-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.operator-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 500;
}

.operator-status.available {
    background: #d4edda;
    color: #155724;
}

.operator-status.busy {
    background: #fff3cd;
    color: #856404;
}

.operator-status.break {
    background: #f8d7da;
    color: #721c24;
}

.operator-timer {
    display: flex;
    align-items: center;
    gap: 10px;
}

.timer-display {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    min-width: 80px;
}

.timer-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.timer-btn.start {
    background: #28a745;
    color: white;
}

.timer-btn.pause {
    background: #ffc107;
    color: white;
}

.timer-btn.stop {
    background: #dc3545;
    color: white;
}

.timer-btn:hover {
    transform: scale(1.1);
}

.department-works {
    min-height: 100px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.5);
}

.department-works.has-works {
    border-style: solid;
    background: white;
}

.work-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.work-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.work-item.urgent {
    border-left-color: #dc3545;
}

.work-item.high {
    border-left-color: #ffc107;
}

.work-item.medium {
    border-left-color: #007bff;
}

.work-item.low {
    border-left-color: #28a745;
}

.work-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.work-vehicle {
    font-weight: 600;
    color: #333;
}

.work-priority {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.work-priority.urgent {
    background: #f8d7da;
    color: #721c24;
}

.work-priority.high {
    background: #fff3cd;
    color: #856404;
}

.work-priority.medium {
    background: #cce7ff;
    color: #004085;
}

.work-priority.low {
    background: #d4edda;
    color: #155724;
}

.work-description {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.work-client {
    font-size: 12px;
    color: #999;
}

/* 📋 WORK LIST */
.work-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.work-list {
    display: grid;
    gap: 15px;
}

.work-list-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 5px solid;
    transition: all 0.3s ease;
    cursor: pointer;
}

.work-list-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.work-list-item.pending {
    border-left-color: #6c757d;
}

.work-list-item.in_progress {
    border-left-color: #ffc107;
}

.work-list-item.completed {
    border-left-color: #28a745;
}

.work-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.work-list-vehicle {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.work-list-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.work-list-status.pending {
    background: #e9ecef;
    color: #495057;
}

.work-list-status.in_progress {
    background: #fff3cd;
    color: #856404;
}

.work-list-status.completed {
    background: #d4edda;
    color: #155724;
}

.work-list-details {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    font-size: 14px;
    color: #666;
}

.work-list-detail {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 📝 FORMS */
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    max-width: 800px;
}

.work-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn-primary,
.btn-secondary {
    padding: 12px 25px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* 📊 REPORTS */
.reports-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.report-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e1e5e9;
}

.report-card h3 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Responsive per reparti */
@media (max-width: 768px) {
    .departments-container {
        grid-template-columns: 1fr;
    }

    .operator-card {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .work-list-details {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-actions {
        justify-content: center;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }
}

/* 🔧 MODAL LAVORO */
.work-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.work-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e1e5e9;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 30px;
}

.work-details {
    margin-bottom: 30px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    font-size: 14px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row strong {
    color: #333;
    font-weight: 600;
    min-width: 120px;
}

.priority-badge,
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-badge.urgent {
    background: #f8d7da;
    color: #721c24;
}

.priority-badge.high {
    background: #fff3cd;
    color: #856404;
}

.priority-badge.medium {
    background: #cce7ff;
    color: #004085;
}

.priority-badge.low {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #e9ecef;
    color: #495057;
}

.status-badge.in_progress {
    background: #fff3cd;
    color: #856404;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.on_hold {
    background: #f8d7da;
    color: #721c24;
}

.work-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.action-btn.assign {
    background: #007bff;
    color: white;
}

.action-btn.start {
    background: #28a745;
    color: white;
}

.action-btn.pause {
    background: #ffc107;
    color: #333;
}

.action-btn.complete {
    background: #28a745;
    color: white;
}

.action-btn.delete {
    background: #dc3545;
    color: white;
}

/* 🍞 TOAST AVANZATO */
.toast {
    animation: slideInRight 0.3s ease;
    position: relative;
    overflow: hidden;
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: currentColor;
}

.toast.success::before {
    background: #28a745;
}

.toast.error::before {
    background: #dc3545;
}

.toast.info::before {
    background: #007bff;
}

.toast-content {
    flex: 1;
    padding-left: 15px;
}

.toast-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.toast-message {
    color: #666;
    font-size: 14px;
}

.toast-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.toast-close:hover {
    background: #f1f3f4;
    color: #333;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 📊 REPORT STYLES */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e1e5e9;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

.stat-value {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.dept-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e1e5e9;
}

.dept-stat:last-child {
    border-bottom: none;
}

.dept-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #333;
}

.dept-numbers {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    font-size: 14px;
    color: #666;
}

/* 🎯 FILTRI E CONTROLLI */
#workFilter {
    padding: 8px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#workFilter:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 📱 RESPONSIVE MODAL */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
        max-height: 90vh;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .work-actions {
        flex-direction: column;
    }

    .action-btn {
        min-width: auto;
        width: 100%;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .detail-row strong {
        min-width: auto;
    }
}

/* 📋 MODAL TABS */
.modal-tabs {
    display: flex;
    border-bottom: 2px solid #e1e5e9;
    margin-bottom: 25px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.modal-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
}

.modal-tab:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.modal-tab.active {
    background: white;
    color: #667eea;
    border-bottom-color: #667eea;
    font-weight: 600;
}

.modal-tab-content {
    display: none;
}

.modal-tab-content.active {
    display: block;
}

/* 📸 SEZIONE FOTO */
.photos-section {
    padding: 20px 0;
}

.photos-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.photos-header h4 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-upload {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-upload:hover {
    background: #218838;
    transform: translateY(-1px);
}

.photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.photo-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.photo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.photo-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 15px 10px 10px;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.photo-item:hover .photo-overlay {
    transform: translateY(0);
}

.photo-info {
    margin-bottom: 8px;
}

.photo-name {
    display: block;
    font-weight: 600;
    font-size: 13px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-date {
    font-size: 11px;
    opacity: 0.8;
}

.photo-delete {
    background: #dc3545;
    color: white;
    border: none;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.photo-delete:hover {
    background: #c82333;
}

.no-photos {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: #999;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.no-photos i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-photos p {
    margin: 0;
    font-size: 16px;
}

/* 📸 FULLSCREEN FOTO */
.photo-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.photo-fullscreen.show {
    opacity: 1;
    visibility: visible;
}

.fullscreen-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.fullscreen-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    text-align: center;
}

.fullscreen-content img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
}

.fullscreen-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: all 0.3s ease;
}

.fullscreen-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.fullscreen-info {
    color: white;
    margin-top: 20px;
}

.fullscreen-info h4 {
    margin: 0 0 5px 0;
    font-size: 18px;
}

.fullscreen-info p {
    margin: 0;
    opacity: 0.8;
    font-size: 14px;
}

/* 🔧 SEZIONE RICAMBI */
.spare-parts-section {
    padding: 20px 0;
}

.parts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.parts-header h4 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.parts-total {
    background: #e8f5e8;
    color: #155724;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 16px;
}

.add-part-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    border: 1px solid #e1e5e9;
}

.add-part-form h5 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.part-form-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 10px;
    align-items: end;
}

.part-form-grid input {
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.part-form-grid input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-add-part {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-add-part:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.parts-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.parts-table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 80px 100px 100px 100px;
    gap: 15px;
    background: #667eea;
    color: white;
    padding: 15px 20px;
    font-weight: 600;
    font-size: 14px;
}

.parts-table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 80px 100px 100px 100px;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e5e9;
    align-items: center;
    transition: all 0.3s ease;
}

.parts-table-row:hover {
    background: #f8f9fa;
}

.parts-table-row:last-child {
    border-bottom: none;
}

.part-name {
    font-weight: 600;
    color: #333;
}

.part-code {
    font-family: 'Courier New', monospace;
    background: #f1f3f4;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #495057;
}

.part-quantity {
    text-align: center;
    font-weight: 600;
}

.part-unit-price,
.part-total-price {
    text-align: right;
    font-weight: 600;
    color: #28a745;
}

.part-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.btn-edit-part,
.btn-delete-part {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
}

.btn-edit-part {
    background: #ffc107;
    color: #333;
}

.btn-edit-part:hover {
    background: #e0a800;
    transform: scale(1.1);
}

.btn-delete-part {
    background: #dc3545;
    color: white;
}

.btn-delete-part:hover {
    background: #c82333;
    transform: scale(1.1);
}

.no-parts {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.no-parts i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-parts p {
    margin: 0;
    font-size: 16px;
}

/* 🏷️ INDICATORI LAVORI */
.work-indicators {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.indicator {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.indicator.photos {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.indicator.parts {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.work-list-indicators {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.list-indicator {
    background: #f8f9fa;
    color: #666;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    border: 1px solid #e1e5e9;
}

.list-indicator.photos {
    border-color: #28a745;
    color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.list-indicator.parts {
    border-color: #ffc107;
    color: #e0a800;
    background: rgba(255, 193, 7, 0.05);
}

/* 📱 RESPONSIVE FOTO E RICAMBI */
@media (max-width: 768px) {
    .photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .photo-item img {
        height: 120px;
    }

    .part-form-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .parts-table-header,
    .parts-table-row {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: left;
    }

    .parts-table-header {
        display: none;
    }

    .parts-table-row {
        background: white;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 15px;
        display: block;
    }

    .parts-table-row > div {
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .parts-table-row > div::before {
        content: attr(data-label);
        font-weight: 600;
        color: #666;
        font-size: 12px;
        text-transform: uppercase;
    }

    .part-actions {
        justify-content: flex-end;
        margin-top: 10px;
    }

    .modal-tabs {
        flex-direction: column;
    }

    .modal-tab {
        border-bottom: 1px solid #e1e5e9;
        border-radius: 0;
    }

    .modal-tab.active {
        border-bottom-color: #667eea;
    }
}

/* 👥 CLIENTS STYLES */
.clients-container {
    padding: 20px;
}

.clients-search {
    margin-bottom: 20px;
}

.clients-search input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
}

.clients-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.client-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.client-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    border-color: #3498db;
}

.client-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.client-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
}

.client-vehicles {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.client-details {
    margin-bottom: 15px;
}

.client-contact {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    color: #666;
    font-size: 14px;
}

.client-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #888;
}

.client-stats .stat {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 💰 ESTIMATES STYLES */
.estimates-container {
    padding: 20px;
}

.estimates-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.estimate-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.estimate-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.estimate-card.pending {
    border-left: 4px solid #f39c12;
}

.estimate-card.approved {
    border-left: 4px solid #27ae60;
}

.estimate-card.converted {
    border-left: 4px solid #3498db;
}

.estimate-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.estimate-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 16px;
}

.estimate-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.estimate-status.pending {
    background: #f39c12;
    color: white;
}

.estimate-status.approved {
    background: #27ae60;
    color: white;
}

.estimate-status.converted {
    background: #3498db;
    color: white;
}

.estimate-details {
    margin-bottom: 15px;
}

.estimate-details > div {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    color: #666;
    font-size: 14px;
}

.estimate-costs {
    margin-bottom: 15px;
}

.cost-breakdown {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.total-cost {
    text-align: right;
    font-size: 16px;
    color: #2c3e50;
}

.estimate-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-pdf {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.btn-pdf:hover {
    background: #c0392b;
}

.btn-convert {
    background: #27ae60;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.btn-convert:hover {
    background: #229954;
}
