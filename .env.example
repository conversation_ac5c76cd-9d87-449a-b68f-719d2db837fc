# 🗄️ DATABASE CONFIGURATION
# Render PostgreSQL Database URL (will be provided by <PERSON>der)
DATABASE_URL=postgresql://username:password@hostname:port/database

# 🔧 APPLICATION CONFIGURATION
NODE_ENV=production
PORT=10000

# 🔒 SECURITY (optional)
JWT_SECRET=your-jwt-secret-here

# 📧 EMAIL CONFIGURATION (for future features)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 📱 NOTIFICATION SETTINGS (for future features)
ENABLE_NOTIFICATIONS=true
NOTIFICATION_EMAIL=<EMAIL>
